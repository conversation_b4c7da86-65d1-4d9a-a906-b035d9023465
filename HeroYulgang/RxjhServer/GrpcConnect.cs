using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.GroupQuest;
using RxjhServer.HelperTools;

namespace RxjhServer
{
    /// <summary>
    /// GRPC-based Connect class thay thế cho TCP Connect.cs
    /// Giữ nguyên interface để tương thích với code hiện tại
    /// </summary>
    public partial class GrpcConnect
    {
        private System.Timers.Timer timer_0;
        private GameServerCommunicationClient _grpcClient;
        private bool _isConnected = false;

        public GrpcConnect()
        {
            _grpcClient = GameServerCommunicationClient.Instance;

            // Timer để kiểm tra kết nối định kỳ
            timer_0 = new(5000.0);
            timer_0.Elapsed += timer_0_Elapsed;
            timer_0.AutoReset = true;
            timer_0.Enabled = true;
        }

        private async void timer_0_Elapsed(object sender, ElapsedEventArgs e)
        {
            if (!_isConnected)
            {
                await SetupAsync();
            }
        }

        /// <summary>
        /// Thiết lập kết nối GRPC - thay thế cho Sestup()
        /// </summary>
        public async Task<bool> SetupAsync()
        {
            try
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info,
                    $"Đang kết nối đến LoginServer qua GRPC - IP: {World.AccountVerificationServerIP}, Port: {World.LoginServerGrpcPort}");

                var connected = await _grpcClient.ConnectAsync();
                if (connected)
                {
                    _isConnected = true;
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug,
                        "LoginServer đã kết nối thành công qua GRPC");

                    // Gửi các message khởi tạo
                    await SendInitialMessages();
                    return true;
                }
                else
                {
                    _isConnected = false;
                    return false;
                }
            }
            catch (Exception ex)
            {
                _isConnected = false;
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"Lỗi kết nối LoginServer qua GRPC: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gửi các message khởi tạo sau khi kết nối
        /// </summary>
        private async Task SendInitialMessages()
        {
            try
            {
                // Gửi message cập nhật port
                await Task.Delay(500);
                await TransmitAsync($"UPDATE_SERVER_PORT|{World.ServerID}|{World.GameServerPort2}");

                // Review user login
                await ReviewUserLoginAsync();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"Lỗi khi gửi message khởi tạo: {ex.Message}");
            }
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (timer_0 != null)
            {
                timer_0.Enabled = false;
                timer_0.Close();
                timer_0.Dispose();
            }

            _grpcClient?.Dispose();
            _isConnected = false;
        }

        /// <summary>
        /// Gửi thông tin review user login - async version
        /// </summary>
        public async Task ReviewUserLoginAsync()
        {
            try
            {
                StringBuilder stringBuilder = new();
                foreach (var value9 in World.list.Values)
                {
                    var value = "NULL";
                    var value2 = 0;
                    if (value9.TreoMay)
                    {
                        value2 = 1;
                    }
                    var value3 = 0;
                    var value4 = string.Empty;
                    var value5 = string.Empty;
                    var value6 = 0;
                    var value7 = string.Empty;
                    var value8 = string.Empty;
                    var players = World.FindPlayerBySession(value9.WorldId);
                    if (players != null)
                    {
                        value = players.UserName;
                        value3 = players.OriginalServerSerialNumber;
                        value4 = players.OriginalServerIP;
                        value5 = players.OriginalServerPort.ToString();
                        value6 = players.OriginalServerID;
                        value7 = players.SilverCoinSquareServerIP;
                        value8 = players.SilverCoinSquareServerPort.ToString();
                    }
                    if (value9.Player == null)
                    {
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Player Null;");
                        return;
                    }
                    stringBuilder.Append(value9.Player.Userid);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value9);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value9.BindAccount);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value2);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value3);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value4);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value5);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value6);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value7);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value8);
                    stringBuilder.Append("-");
                    stringBuilder.Append(value9.WorldId);
                    stringBuilder.Append(",");
                }
                if (stringBuilder.Length > 0)
                {
                    stringBuilder.Remove(stringBuilder.Length - 1, 1);
                }
                await TransmitAsync("REVIEW_USER_LOGIN|" + stringBuilder);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"Review Nguoi Choi Dang Nhap error: {ex.Message}");
            }
        }

        /// <summary>
        /// Transmit method - giữ nguyên signature để tương thích
        /// </summary>
        public void Transmit(string message)
        {
            // Chuyển sang async version
            _ = Task.Run(async () => await TransmitAsync(message));
        }

        /// <summary>
        /// Async version của Transmit
        /// </summary>
        public async Task<bool> TransmitAsync(string message)
        {
            try
            {
                if (!_isConnected)
                {
                    // Thử kết nối lại
                    var reconnected = await _grpcClient.EnsureConnectedAsync();
                    if (!reconnected)
                    {
                        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Warning,
                            "Không thể gửi message - chưa kết nối đến LoginServer");
                        return false;
                    }
                    _isConnected = true;
                }

                var success = await _grpcClient.TransmitAsync(message);
                if (!success)
                {
                    _isConnected = false;
                }
                return success;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"GRPC Transmit error: {message} - {ex.Message}");
                _isConnected = false;
                return false;
            }
        }

        /// <summary>
        /// Transmit với response data
        /// </summary>
        public async Task<(bool Success, string[] ResponseData)> TransmitWithResponseAsync(string message)
        {
            try
            {
                if (!_isConnected)
                {
                    var reconnected = await _grpcClient.EnsureConnectedAsync();
                    if (!reconnected)
                    {
                        return (false, Array.Empty<string>());
                    }
                    _isConnected = true;
                }

                var result = await _grpcClient.TransmitWithResponseAsync(message);
                if (!result.Success)
                {
                    _isConnected = false;
                }
                return result;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,
                    $"GRPC TransmitWithResponse error: {message} - {ex.Message}");
                _isConnected = false;
                return (false, Array.Empty<string>());
            }
        }

        /// <summary>
        /// Kiểm tra trạng thái kết nối
        /// </summary>
        public bool IsConnected => _isConnected && _grpcClient.IsConnected;

        /// <summary>
        /// Gửi hành động trong Zone liên server đến LoginServer
        /// </summary>
        public void SendCrossServerAction(int zoneId, string actionType, int sessionId, params object[] parameters)
        {
            try
            {
                // Tạo chuỗi thông tin hành động
                StringBuilder sb = new StringBuilder();
                sb.Append("CROSS_SERVER_ZONE_ACTION|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(actionType);
                sb.Append("|");
                sb.Append(sessionId);

                // Thêm các tham số
                foreach (var param in parameters)
                {
                    sb.Append("|");
                    sb.Append(param);
                }

                // Gửi thông tin hành động đến LoginServer
                Transmit(sb.ToString());
                if (actionType != "GET_NPCS")
                    LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi hành động {actionType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi hành động Zone liên server: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi dữ liệu broadcast trong Zone liên server đến LoginServer
        /// </summary>
        public void SendCrossServerBroadcast(int zoneId, string dataType, int sessionId, string hexData, int id, int wordid)
        {
            try
            {
                // Tạo chuỗi thông tin broadcast
                StringBuilder sb = new();
                sb.Append("CROSS_SERVER_ZONE_BROADCAST|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(dataType);
                sb.Append("|");
                sb.Append(sessionId);
                sb.Append("|");
                sb.Append(hexData);
                sb.Append("|");
                sb.Append(id);
                sb.Append("|");
                sb.Append(wordid);

                // Gửi thông tin broadcast đến LoginServer
                Transmit(sb.ToString());

                //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi broadcast {dataType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi broadcast Zone liên server: {ex.Message}");
            }
        }

        /// <summary>
        /// Gửi dữ liệu broadcast full trong Zone liên server đến LoginServer
        /// </summary>
        public void SendCrossServerBroadcast(int zoneId, string dataType, int sessionId, string hexData)
        {
            try
            {
                // Tạo chuỗi thông tin broadcast
                StringBuilder sb = new();
                sb.Append("CROSS_SERVER_ZONE_BROADCAST_FULL|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(dataType);
                sb.Append("|");
                sb.Append(sessionId);
                sb.Append("|");
                sb.Append(hexData);
                sb.Append("|");

                // Gửi thông tin broadcast đến LoginServer
                Transmit(sb.ToString());

                //LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi broadcast {dataType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi broadcast Zone liên server: {ex.Message}");
            }
        }
        public void SendPlayerKill(Players victim, Players killer, Players[] contributors)
        {
            try
            {
                var message = $"PLAYER_KILL|{World.ServerID}|{victim.SessionID}|{victim.UserName}|{victim.Player_Level}|{victim.GuildId}|{victim.Player_Zx}|{World.ServerID}|{killer.SessionID}|{killer.UserName}|{killer.Player_Level}|{killer.GuildId}|{killer.Player_Zx}";
                foreach (var contributor in contributors)
                {
                    if (contributor == null || contributor == killer || contributor == victim)
                        continue;
                    message += $"|{World.ServerID}|{contributor.SessionID}|{victim.UserName}|{contributor.Player_Level}|{contributor.GuildId}|{contributor.Player_Zx}";
                }
                Transmit(message);
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi thông tin tiêu diệt người chơi đến LS: Killer ID {killer.SessionID}, Victim ID {victim.SessionID}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi thông tin tiêu diệt người chơi đến LS: {ex.Message}");
            }
        }
        public void SendCrossServerNpcBroadcast(int zoneId, int npcId, string dataType, string hexData, int id, int wordid)
        {
            try
            {
                // Tạo chuỗi thông tin broadcast
                StringBuilder sb = new();
                sb.Append("CROSS_SERVER_ZONE_NPC_BROADCAST|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(npcId);
                sb.Append("|");
                sb.Append(dataType);
                sb.Append("|");
                sb.Append(hexData);
                sb.Append("|");
                sb.Append(id);
                sb.Append("|");
                sb.Append(wordid);

                // Gửi thông tin broadcast đến LoginServer
                Transmit(sb.ToString());

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi NPC broadcast {dataType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi NPC broadcast Zone liên server: {ex.Message}");
            }
        }
        public void SendCrossServerNpcBroadcast(int zoneId, int npcId, string dataType, string hexData)
        {
            try
            {
                // Tạo chuỗi thông tin broadcast
                StringBuilder sb = new();
                sb.Append("CROSS_SERVER_ZONE_NPC_BROADCAST_FULL|");
                sb.Append(World.ServerID);
                sb.Append("|");
                sb.Append(zoneId);
                sb.Append("|");
                sb.Append(npcId);
                sb.Append("|");
                sb.Append(dataType);
                sb.Append("|");
                sb.Append(hexData);
                sb.Append("|");

                // Gửi thông tin broadcast đến LoginServer
                Transmit(sb.ToString());

                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi NPC broadcast {dataType} trong Zone {zoneId} đến LoginServer");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi gửi NPC broadcast Zone liên server: {ex.Message}");
            }
        }

        public void CreateCloneNpc(int npcSessionId, string npcWorldId, int zoneId, NpcClass npc)
        {
            try
            {
                // Gửi yêu cầu lấy thông tin NPC từ server gốc
                // Chuyển float sang hex để đảm bảo độ chính xác
                var hexX = BitConverter.GetBytes(npc.Rxjh_cs_X);
                var hexY = BitConverter.GetBytes(npc.Rxjh_cs_Y);
                SendCrossServerAction(
                    zoneId,
                    "CREATE_NPC_CLONE",
                    0, // Không cần sessionId của người chơi
                    npcSessionId,
                    npcWorldId,
                    npc.FLD_PID,
                    BitConverter.ToString(hexX).Replace("-", ""),
                    BitConverter.ToString(hexY).Replace("-", ""),
                    npc.Rxjh_Map,
                    npc.Rxjh_HP,
                    npc.Max_Rxjh_HP
                );

            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi khi tạo NPC clone: {ex.Message}");
            }
        }

        /// <summary>
	/// Gửi cập nhật tiến trình quest đến LS
	/// </summary>
	/// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
	/// <param name="questId">ID của quest</param>
	/// <param name="groupId">ID của guild hoặc faction</param>
	/// <param name="currentCount">Số lượng hiện tại</param>
	/// <param name="status">Trạng thái quest</param>
	public void SendGroupQuestUpdate(int questType, int questId, int groupId, int currentCount, int status)
	{
		try
		{
			Transmit($"GROUP_QUEST_UPDATE|{questType}|{questId}|{groupId}|{currentCount}|{status}");
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi cập nhật quest đến LS: Type {questType}, Quest ID {questId}, Group ID {groupId}, Count {currentCount}, Status {status}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi cập nhật quest đến LS: {ex.Message}");
		}
	}

	/// <summary>
	/// Gửi thông tin đóng góp quest đến LS
	/// </summary>
	/// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
	/// <param name="questId">ID của quest</param>
	/// <param name="groupId">ID của guild hoặc faction</param>
	/// <param name="playerId">ID của người chơi</param>
	/// <param name="playerName">Tên người chơi</param>
	/// <param name="contributionCount">Số lượng đóng góp</param>
	public void SendGroupQuestContribution(int questType, int npcId, int npcLevel, int groupId, int playerId, string playerName, int playerLevel, int contributionCount)
	{
		try
		{
			Transmit($"MONSTER_KILL|{questType}|{npcId}|{npcLevel}|{groupId}|{playerId}|{playerName}|{playerLevel}|{contributionCount}|{World.ServerID}");
			//LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi đóng góp quest đến LS: Type {questType}, NpcId {npcId},Npc Level {npcLevel}, Group ID {groupId}, Player ID {playerId}, Count {contributionCount}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi đóng góp quest đến LS: {ex.Message}");
		}
	}

    /// <summary>
	/// Gửi thông báo hoàn thành quest đến LS
	/// </summary>
	/// <param name="questType">Loại quest (1: Guild, 2: Faction)</param>
	/// <param name="questId">ID của quest</param>
	/// <param name="groupId">ID của guild hoặc faction</param>
	public void SendGroupQuestComplete(int questType, int questId, int groupId)
	{
		try
		{
			Transmit($"GROUP_QUEST_COMPLETE|{questType}|{questId}|{groupId}");
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi hoàn thành quest đến LS: Type {questType}, Quest ID {questId}, Group ID {groupId}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi gửi hoàn thành quest đến LS: {ex.Message}");
		}
	}


	/// <summary>
	/// Xử lý thông tin đóng góp quest từ LS
	/// </summary>
	/// <param name="array">Mảng chứa thông tin đóng góp</param>
	private void ProcessGroupQuestContribution(string[] array)
	{
		try
		{
			if (array.Length < 7)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin đóng góp quest");
				return;
			}

			int questType = int.Parse(array[1]);
			int targetId = int.Parse(array[2]);
			int targetLevel = int.Parse(array[3]);
			int groupId = int.Parse(array[4]);
			int playerId = int.Parse(array[5]);
			string playerName = array[6];
			int contributionCount = int.Parse(array[7]);

			// Thông báo cho người chơi
			string questTypeName = questType == 1 ? "bang hội" : "phe phái";
			string groupName = questType == 1 ? $"Bang hội ID {groupId}" : (groupId == 1 ? "Chính Phái" : "Tà Phái");

			// Gửi thông báo đến người chơi đã đóng góp
			Players contributor = World.FindPlayerBySession(playerId);
			if (contributor != null && !contributor.Client.TreoMay)
			{
				contributor.HeThongNhacNho($"Bạn đã đóng góp {contributionCount} cho nhiệm vụ {questTypeName} #{targetId}!", 10, "");
			}

			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã nhận đóng góp quest từ LS: Type {questType}, Quest ID {targetId}, Group ID {groupId}, Player ID {playerId}, Count {contributionCount}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý đóng góp quest: {ex.Message}");
		}
	}

	/// <summary>
	/// Xử lý thông báo hoàn thành quest từ LS
	/// </summary>
	/// <param name="array">Mảng chứa thông tin hoàn thành</param>
	private void ProcessGroupQuestComplete(string[] array)
	{
		try
		{
			if (array.Length < 3)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Thiếu thông tin hoàn thành quest");
				return;
			}

			int guildId = int.Parse(array[1]);
			var message = array[2];
			int questId = int.Parse(array[3]);
			int progressId = int.Parse(array[4]);
			int progressCount = int.Parse(array[5]);

			foreach (var player in World.allConnectedChars.Values)
			{
				if (player != null && !player.Client.TreoMay)
				{
					if (player.GuildId == guildId)
					{
						player.HeThongNhacNho($"#{questId}-{World.FromBase64(message)} đã hoàn thành!!", 10, "");
						if (World.IsGuildMaster(player))
						{
							if (player.QuestList.ContainsKey(questId))
								player.QuestList.Remove(questId);
							player.TaskPromptDataSending(questId, 51, 1);
						}
						//TODO:
						// Chia phần thưởng
						GroupQuestEvent.Instance.ReceiveGuildQuestReward(player, questId);
					}
				}

			}

		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi xử lý hoàn thành quest: {ex.Message}");
		}
	}



	/// <summary>
	/// Kiểm tra xem người chơi có đóng góp cho quest không
	/// </summary>
	public bool CheckPlayerContribution(int questType, int questId, int groupId, int playerId)
	{
		try
		{
			// Trong thực tế, chúng ta sẽ gửi request đến LS để kiểm tra
			// Ở đây chỉ giả lập việc kiểm tra
			return true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi kiểm tra đóng góp quest: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Kiểm tra xem người chơi đã nhận thưởng chưa
	/// </summary>
	public bool CheckRewardReceived(int questType, int questId, int groupId, int playerId)
	{
		try
		{
			// Trong thực tế, chúng ta sẽ gửi request đến LS để kiểm tra
			// Ở đây chỉ giả lập việc kiểm tra
			return false;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi kiểm tra nhận thưởng quest: {ex.Message}");
			return false;
		}
	}

	/// <summary>
	/// Đánh dấu người chơi đã nhận thưởng
	/// </summary>
	public void MarkRewardReceived(int questType, int questId, int groupId, int playerId)
	{
		try
		{
			// Gửi thông tin đến LS để đánh dấu đã nhận thưởng
			Transmit($"GROUP_QUEST_MARK_REWARD|{questType}|{questId}|{groupId}|{playerId}");
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Info, $"Đã gửi đánh dấu nhận thưởng đến LS: Type {questType}, Quest ID {questId}, Group ID {groupId}, Player ID {playerId}");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"Lỗi đánh dấu nhận thưởng quest: {ex.Message}");
		}
	}

        // Giữ lại các method cũ để tương thích backward
        public void ReviewUserLogin()
        {
            _ = Task.Run(async () => await ReviewUserLoginAsync());
        }

        public void Sestup()
        {
            _ = Task.Run(async () => await SetupAsync());
        }
    }
}
